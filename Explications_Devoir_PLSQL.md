# Explications Détaillées du Devoir PL/SQL

## Analyse des Solutions Basées sur le Support de Cours

### Configuration Initiale
Selon le support de cours, il est important de configurer SQL*PLUS correctement :
```sql
SET LINESIZE 120    -- Évite la coupure du texte
SET PAGESIZE 60     -- Nombre de lignes par page
SET SERVEROUTPUT ON -- Indispensable pour DBMS_OUTPUT.PUT_LINE
```

---

## 1°) Programme de Conversion de Devise

### Concepts du Cours Utilisés :
- **ACCEPT** : Recommandé dans le cours pour la saisie utilisateur sécurisée
- **Variables de substitution** : `&variable` pour récupérer les valeurs
- **Validation des données** : Contrôles avec IF/THEN
- **Formatage** : TO_CHAR pour l'affichage formaté

### Points Clés :
```sql
ACCEPT montant_fcfa PROMPT 'Entrez le montant en FCFA: '
```
Cette approche est préférée à la saisie directe avec `&` car elle est plus claire pour l'utilisateur.

---

## 2°) Fonction f_employes

### Concepts du Cours Utilisés :
- **%TYPE** : Héritage du type de colonne (`EMP.EMPNO%TYPE`)
- **SELECT INTO** : Pour récupérer une seule ligne
- **Gestion d'erreurs** : EXCEPTION avec NO_DATA_FOUND

### Structure Fonction (selon le cours) :
```sql
CREATE OR REPLACE FUNCTION nom_fonction (paramètres)
RETURN type_retour
IS
  -- Déclarations
BEGIN
  -- Instructions
  RETURN valeur;
EXCEPTION
  -- Gestion erreurs
END;
```

---

## 3°) Fonction F_PRIME

### Calcul de Prime :
- Prime = 20% du salaire
- Utilisation de `ROUND()` pour arrondir à 2 décimales
- Retour de 0 en cas d'erreur (employé inexistant)

---

## 4°) Requête SELECT avec Fonction

### Utilisation de Fonction dans SELECT :
Le cours montre qu'on peut appeler une fonction dans un SELECT :
```sql
SELECT ename, sal, f_impot(sal) AS impot FROM emp;
```

Notre solution suit ce modèle :
```sql
SELECT EMPNO, ENAME, SAL, F_PRIME(EMPNO) AS "Prime (20%)"
```

---

## 5°) Procédure P_Modif

### Concepts du Cours Utilisés :
- **Procédure** : Bloc PL/SQL sans valeur de retour
- **UPDATE** : Modification des données
- **COMMIT/ROLLBACK** : Gestion des transactions
- **SQL%ROWCOUNT** : Nombre de lignes affectées

### Structure Procédure (selon le cours) :
```sql
CREATE OR REPLACE PROCEDURE nom_procedure (paramètres)
IS
  -- Déclarations
BEGIN
  -- Instructions
EXCEPTION
  -- Gestion erreurs
END;
```

---

## 6°) Trigger de Sécurité

### Concepts du Cours Utilisés :
- **Trigger FOR EACH ROW** : S'exécute pour chaque ligne
- **:NEW et :OLD** : Valeurs avant/après modification
- **RAISE_APPLICATION_ERROR** : Génération d'erreur personnalisée
- **INSERTING/UPDATING/DELETING** : Prédicats pour identifier l'opération

### Types de Triggers (selon le cours) :
1. **Niveau ligne** : FOR EACH ROW (notre cas)
2. **Niveau table** : Une fois par instruction
3. **Niveau base** : Événements système (LOGON/LOGOFF)

---

## Bonnes Pratiques Appliquées

### 1. Nommage :
- Fonctions : `f_` ou `F_` (F_PRIME)
- Procédures : `p_` ou `P_` (P_Modif)
- Triggers : `t_` ou `TR_` (TR_SALAIRE_LIMITE)

### 2. Gestion d'Erreurs :
- Toujours inclure EXCEPTION
- Messages d'erreur explicites
- Utilisation de ROLLBACK en cas d'erreur

### 3. Types de Données :
- Utilisation de `%TYPE` pour la compatibilité
- Validation des paramètres d'entrée

### 4. Transactions :
- COMMIT après succès
- ROLLBACK en cas d'erreur

---

## Tests et Validation

Le fichier inclut des tests complets pour :
- Vérifier le bon fonctionnement des fonctions
- Tester les cas d'erreur
- Valider le comportement du trigger
- Démontrer l'utilisation pratique

### Exemple de Test :
```sql
BEGIN
    DBMS_OUTPUT.PUT_LINE(f_employes(7369)); -- Test normal
    DBMS_OUTPUT.PUT_LINE(f_employes(9999)); -- Test erreur
END;
```

---

## Correspondance avec le Support de Cours

Chaque solution respecte les patterns et syntaxes présentés dans le cours :
- Structure des blocs PL/SQL
- Utilisation des curseurs (si nécessaire)
- Gestion des erreurs
- Bonnes pratiques de codage

Le code est prêt pour l'exécution dans l'environnement Oracle configuré selon les instructions du cours.
