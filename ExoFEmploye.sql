---- creer une fonction nommee f_employe qui affiche le nom ,fonction et salaire d'un employé dont le matricule est defini par l'utilisateur
create or replace function f_employe(v_matricule emp.empno%TYPE)
RETURN varchar2 is 
    v_nom emp.ename%TYPE;
    v_fonction emp.job%TYPE;
    v_salaire emp.sal%TYPE;
    v_employe varchar2(100);
BEGIN
    select ename,job,sal
    into v_nom,v_fonction,v_salaire
    from emp
    where empno = v_matricule;
    v_employe:=v_nom||' est un '||v_fonction||' avec un salaire de $'||v_salaire;
    RETURN v_employe;
END;
/
