-- Devoir PL/SQL - <PERSON> Fall L3GL2
-- Basé sur le support de cours et les exercices existants
-- Schéma SCOTT avec tables EMP et DEPT

-- Configuration SQL*PLUS (selon le cours)
SET LINESIZE 120
SET PAGESIZE 60
SET SERVEROUTPUT ON

-- =====================================================
-- 1°) Programme de conversion de montant FCFA vers devise étrangère
-- =====================================================
-- Basé sur l'exemple ACCEPT du cours et Exoaccept.sql

ACCEPT montant_fcfa PROMPT 'Entrez le montant en FCFA: '
ACCEPT taux_change PROMPT 'Entrez le taux de change: '
ACCEPT devise PROMPT 'Entrez le nom de la devise: '

DECLARE
    v_montant_fcfa NUMBER := &montant_fcfa;
    v_taux_change NUMBER := &taux_change;
    v_montant_converti NUMBER;
    v_devise VARCHAR2(10) := '&devise';
BEGIN
    -- Calcul de la conversion
    v_montant_converti := v_montant_fcfa / v_taux_change;
    
    -- Affichage du résultat (inspiré du cours)
    Dbms_output.put_line('Montant en FCFA: ' || v_montant_fcfa);
    Dbms_output.put_line('Taux de change: ' || v_taux_change);
    Dbms_output.put_line('Montant converti en ' || v_devise || ': ' || v_montant_converti);
END;
/

-- =====================================================
-- 2°) Fonction f_employes qui affiche nom, fonction et salaire
-- =====================================================
-- Basé sur ExoFEmploye.sql et l'exemple du cours

CREATE OR REPLACE FUNCTION f_employes(p_matricule IN emp.empno%TYPE)
RETURN VARCHAR2
IS
    v_nom emp.ename%TYPE;
    v_fonction emp.job%TYPE;
    v_salaire emp.sal%TYPE;
    v_resultat VARCHAR2(200);
BEGIN
    -- SELECT INTO comme dans le cours
    SELECT ename, job, sal
    INTO v_nom, v_fonction, v_salaire
    FROM emp
    WHERE empno = p_matricule;
    
    -- Construction du message (inspiré d'ExoFEmploye.sql)
    v_resultat := v_nom || ' est un ' || v_fonction || ' avec un salaire de $' || v_salaire;
    
    -- Affichage
    Dbms_output.put_line(v_resultat);
    
    RETURN v_resultat;
END;
/

-- =====================================================
-- 3°) Fonction F_PRIME pour calculer la prime (20% du salaire)
-- =====================================================
-- Basé sur l'exemple f_impot du cours et ExoFImpot.sql

CREATE OR REPLACE FUNCTION F_PRIME(p_matricule IN emp.empno%TYPE)
RETURN NUMBER
IS
    v_salaire emp.sal%TYPE;
    v_prime NUMBER;
BEGIN
    -- Récupération du salaire
    SELECT sal
    INTO v_salaire
    FROM emp
    WHERE empno = p_matricule;
    
    -- Calcul de la prime (20% du salaire)
    v_prime := v_salaire * 0.20;
    
    RETURN v_prime;
END;
/

-- =====================================================
-- 4°) Instruction SELECT pour afficher les informations avec la prime
-- =====================================================
-- Basé sur l'exemple d'appel de fonction dans SELECT du cours

SELECT 
    empno AS "Matricule",
    ename AS "Nom", 
    sal AS "Salaire Mensuel",
    F_PRIME(empno) AS "Prime",
    (sal + F_PRIME(empno)) AS "Salaire Net"
FROM emp
ORDER BY empno;

-- =====================================================
-- 5°) Procédure P_Modif pour augmenter le salaire de 500$
-- =====================================================
-- Basé sur l'exemple de procédure p_employe du cours

CREATE OR REPLACE PROCEDURE P_Modif(p_nom IN emp.ename%TYPE)
IS
    v_count NUMBER;
BEGIN
    -- Vérifier si l'employé existe
    SELECT COUNT(*)
    INTO v_count
    FROM emp
    WHERE ename = p_nom;
    
    IF v_count = 0 THEN
        Dbms_output.put_line('Aucun employé trouvé avec le nom: ' || p_nom);
    ELSE
        -- Augmenter le salaire de 500$
        UPDATE emp
        SET sal = sal + 500
        WHERE ename = p_nom;
        
        Dbms_output.put_line('Salaire de ' || p_nom || ' augmenté de 500$');
    END IF;
END;
/

-- =====================================================
-- 6°) Trigger pour annuler les opérations sur salaires > 2500
-- =====================================================
-- Basé sur ExoTVerifSal.sql et l'exemple t_verifsal du cours

CREATE OR REPLACE TRIGGER TR_SALAIRE_LIMITE
BEFORE INSERT OR UPDATE OR DELETE ON emp
FOR EACH ROW
BEGIN
    -- Pour INSERT et UPDATE
    IF INSERTING OR UPDATING THEN
        IF :NEW.sal > 2500 THEN
            RAISE_APPLICATION_ERROR(-20001, 'Opération interdite: salaire supérieur à 2500$');
        END IF;
    END IF;
    
    -- Pour DELETE
    IF DELETING THEN
        IF :OLD.sal > 2500 THEN
            RAISE_APPLICATION_ERROR(-20002, 'Impossible de supprimer un employé avec salaire > 2500$');
        END IF;
    END IF;
END;
/

-- =====================================================
-- EXEMPLES D'UTILISATION (basés sur les patterns du cours)
-- =====================================================

-- Test de la fonction f_employes (comme EXEC dans le cours)
BEGIN
    Dbms_output.put_line('Test fonction f_employes:');
    Dbms_output.put_line(f_employes(7369)); -- SMITH
    Dbms_output.put_line(f_employes(7839)); -- KING
END;
/

-- Test de la fonction F_PRIME avec SELECT (comme dans le cours)
SELECT
    ename,
    sal,
    F_PRIME(empno) AS prime
FROM emp
WHERE empno IN (7369, 7839, 7902);

-- Test de la procédure P_Modif (comme EXEC dans le cours)
EXEC P_Modif('SMITH');

-- Vérification après modification
SELECT ename, sal FROM emp WHERE ename = 'SMITH';

-- Test du trigger (cette instruction devrait échouer)
-- INSERT INTO emp VALUES (9999, 'TEST', 'CLERK', 7902, SYSDATE, 3000, NULL, 20);

-- =====================================================
-- SCRIPT DE DÉMONSTRATION COMPLET
-- =====================================================

-- Affichage de tous les employés avec leurs primes
PROMPT === RAPPORT COMPLET DES EMPLOYÉS ===
SELECT
    empno AS "Matricule",
    ename AS "Nom",
    job AS "Fonction",
    sal AS "Salaire",
    F_PRIME(empno) AS "Prime (20%)",
    (sal + F_PRIME(empno)) AS "Salaire Net"
FROM emp
ORDER BY sal DESC;
