--programe qui calcule l'impot  sur le salaire des empolyes .
--le taux d'imposition  est de 10% si le salaire est superieur a 1500$, appliquer 5% entre 1000$ et 1500$,sinon appliquer 2%.
create or replace function f_impot(v_matricule emp.empno%TYPE)
RETURN number is
    v_nom emp.ename%TYPE;
    v_salaire emp.sal%TYPE;
    v_impot number;
BEGIN
    select ename,sal
    into v_nom,v_salaire
    from emp 
    where empno = v_matricule;

    if(v_salaire>1500) then
        v_impot := v_salaire*0.1;
        RETURN v_impot;
    elsif(v_salaire between 1000 and 1500) then
        v_impot := v_salaire*0.05;
        RETURN v_impot;
    else
        v_impot := v_salaire*0.02;
        RETURN v_impot;
    end if;
END;
/       

        

