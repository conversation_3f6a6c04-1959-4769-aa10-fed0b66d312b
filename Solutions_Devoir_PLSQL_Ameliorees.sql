-- Solutions du Devoir PL/SQL L3GL G2 ISI - Version Améliorée
-- Prof: Bobo DIALLO
-- Basé sur le support de cours NoteOracle.pdf
-- Schéma SCOTT avec tables EMP et DEPT

-- Configuration recommandée
SET LINESIZE 120
SET PAGESIZE 60
SET SERVEROUTPUT ON

-- =====================================================
-- 1°) Programme de conversion de montant FCFA vers devise étrangère
-- =====================================================

-- Version avec ACCEPT (recommandée selon le cours)
ACCEPT montant_fcfa PROMPT 'Entrez le montant en FCFA: '
ACCEPT taux_change PROMPT 'Entrez le taux de change: '
ACCEPT devise PROMPT 'Entrez le nom de la devise: '

DECLARE
    v_montant_fcfa NUMBER := &montant_fcfa;
    v_taux_change NUMBER := &taux_change;
    v_montant_converti NUMBER;
    v_devise VARCHAR2(10) := '&devise';
BEGIN
    -- Validation des entrées
    IF v_montant_fcfa <= 0 THEN
        DBMS_OUTPUT.PUT_LINE('Erreur: Le montant doit être positif');
        RETURN;
    END IF;
    
    IF v_taux_change <= 0 THEN
        DBMS_OUTPUT.PUT_LINE('Erreur: Le taux de change doit être positif');
        RETURN;
    END IF;
    
    -- Calcul de la conversion
    v_montant_converti := v_montant_fcfa / v_taux_change;
    
    -- Affichage du résultat formaté
    DBMS_OUTPUT.PUT_LINE('=== CONVERSION DE DEVISE ===');
    DBMS_OUTPUT.PUT_LINE('Montant en FCFA: ' || TO_CHAR(v_montant_fcfa, '999,999,999.99'));
    DBMS_OUTPUT.PUT_LINE('Taux de change: ' || v_taux_change);
    DBMS_OUTPUT.PUT_LINE('Montant converti en ' || v_devise || ': ' || TO_CHAR(ROUND(v_montant_converti, 2), '999,999,999.99'));
END;
/

-- =====================================================
-- 2°) Fonction f_employes qui reçoit le matricule et affiche les infos
-- =====================================================

CREATE OR REPLACE FUNCTION f_employes(p_empno IN EMP.EMPNO%TYPE)
RETURN VARCHAR2
IS
    v_ename EMP.ENAME%TYPE;
    v_job EMP.JOB%TYPE;
    v_sal EMP.SAL%TYPE;
    v_result VARCHAR2(200);
BEGIN
    -- Récupération des données de l'employé (SELECT INTO comme dans le cours)
    SELECT ENAME, JOB, SAL
    INTO v_ename, v_job, v_sal
    FROM EMP
    WHERE EMPNO = p_empno;
    
    -- Construction du message de sortie
    v_result := 'Nom: ' || v_ename || ', Fonction: ' || v_job || ', Salaire: ' || v_sal || '$';
    
    -- Affichage (comme demandé dans l'énoncé)
    DBMS_OUTPUT.PUT_LINE(v_result);
    
    RETURN v_result;
    
EXCEPTION
    WHEN NO_DATA_FOUND THEN
        v_result := 'Aucun employé trouvé avec le matricule: ' || p_empno;
        DBMS_OUTPUT.PUT_LINE(v_result);
        RETURN v_result;
    WHEN OTHERS THEN
        v_result := 'Erreur lors de la recherche: ' || SQLERRM;
        DBMS_OUTPUT.PUT_LINE(v_result);
        RETURN v_result;
END f_employes;
/

-- =====================================================
-- 3°) Fonction F_PRIME pour calculer la prime (20% du salaire)
-- =====================================================

CREATE OR REPLACE FUNCTION F_PRIME(p_empno IN EMP.EMPNO%TYPE)
RETURN NUMBER
IS
    v_sal EMP.SAL%TYPE;
    v_prime NUMBER;
BEGIN
    -- Récupération du salaire de l'employé
    SELECT SAL
    INTO v_sal
    FROM EMP
    WHERE EMPNO = p_empno;
    
    -- Calcul de la prime (20% du salaire)
    v_prime := v_sal * 0.20;
    
    RETURN ROUND(v_prime, 2); -- Arrondi à 2 décimales
    
EXCEPTION
    WHEN NO_DATA_FOUND THEN
        RETURN 0; -- Retourne 0 si l'employé n'existe pas
    WHEN OTHERS THEN
        RETURN 0; -- Retourne 0 en cas d'erreur
END F_PRIME;
/

-- =====================================================
-- 4°) Instruction SELECT pour afficher matricule, nom, salaire, prime et salaire net
-- =====================================================

SELECT
    EMPNO AS "Matricule",
    ENAME AS "Nom",
    SAL AS "Salaire Mensuel",
    F_PRIME(EMPNO) AS "Prime (20%)",
    (SAL + F_PRIME(EMPNO)) AS "Salaire Net"
FROM EMP
ORDER BY EMPNO;

-- =====================================================
-- 5°) Procédure P_Modif pour augmenter le salaire de 500$
-- =====================================================

CREATE OR REPLACE PROCEDURE P_Modif(p_ename IN EMP.ENAME%TYPE)
IS
    v_count NUMBER;
    v_ancien_salaire EMP.SAL%TYPE;
    v_nouveau_salaire EMP.SAL%TYPE;
BEGIN
    -- Vérifier si l'employé existe et récupérer son salaire actuel
    SELECT COUNT(*), NVL(MAX(SAL), 0)
    INTO v_count, v_ancien_salaire
    FROM EMP
    WHERE UPPER(ENAME) = UPPER(p_ename);

    IF v_count = 0 THEN
        DBMS_OUTPUT.PUT_LINE('Aucun employé trouvé avec le nom: ' || p_ename);
    ELSE
        -- Calculer le nouveau salaire
        v_nouveau_salaire := v_ancien_salaire + 500;

        -- Augmenter le salaire de 500$
        UPDATE EMP
        SET SAL = SAL + 500
        WHERE UPPER(ENAME) = UPPER(p_ename);

        -- Messages informatifs
        DBMS_OUTPUT.PUT_LINE('=== MODIFICATION SALAIRE ===');
        DBMS_OUTPUT.PUT_LINE('Employé: ' || p_ename);
        DBMS_OUTPUT.PUT_LINE('Ancien salaire: ' || v_ancien_salaire || '$');
        DBMS_OUTPUT.PUT_LINE('Nouveau salaire: ' || v_nouveau_salaire || '$');
        DBMS_OUTPUT.PUT_LINE('Augmentation: 500$');
        DBMS_OUTPUT.PUT_LINE(SQL%ROWCOUNT || ' ligne(s) mise(s) à jour');

        COMMIT;
    END IF;

EXCEPTION
    WHEN OTHERS THEN
        DBMS_OUTPUT.PUT_LINE('Erreur lors de la modification: ' || SQLERRM);
        ROLLBACK;
END P_Modif;
/

-- =====================================================
-- 6°) Trigger pour annuler les opérations LMD sur employés avec salaire > 2500
-- =====================================================

CREATE OR REPLACE TRIGGER TR_SALAIRE_LIMITE
    BEFORE INSERT OR UPDATE OR DELETE ON EMP
    FOR EACH ROW
BEGIN
    -- Pour INSERT et UPDATE, vérifier le nouveau salaire
    IF INSERTING OR UPDATING THEN
        IF :NEW.SAL > 2500 THEN
            RAISE_APPLICATION_ERROR(-20001,
                'Opération interdite: Le salaire ne peut pas dépasser 2500$. ' ||
                'Salaire proposé: ' || :NEW.SAL || '$');
        END IF;
    END IF;

    -- Pour DELETE, vérifier l'ancien salaire
    IF DELETING THEN
        IF :OLD.SAL > 2500 THEN
            RAISE_APPLICATION_ERROR(-20002,
                'Opération interdite: Impossible de supprimer un employé avec un salaire > 2500$. ' ||
                'Salaire actuel: ' || :OLD.SAL || '$');
        END IF;
    END IF;
END TR_SALAIRE_LIMITE;
/

-- =====================================================
-- EXEMPLES D'UTILISATION ET TESTS
-- =====================================================

-- Test de la fonction f_employes
PROMPT === Test de la fonction f_employes ===
BEGIN
    DBMS_OUTPUT.PUT_LINE('Test avec SMITH (7369):');
    DBMS_OUTPUT.PUT_LINE(f_employes(7369));
    DBMS_OUTPUT.PUT_LINE('');

    DBMS_OUTPUT.PUT_LINE('Test avec KING (7839):');
    DBMS_OUTPUT.PUT_LINE(f_employes(7839));
    DBMS_OUTPUT.PUT_LINE('');

    DBMS_OUTPUT.PUT_LINE('Test avec matricule inexistant (9999):');
    DBMS_OUTPUT.PUT_LINE(f_employes(9999));
END;
/

-- Test de la fonction F_PRIME
PROMPT === Test de la fonction F_PRIME ===
SELECT
    EMPNO,
    ENAME,
    SAL,
    F_PRIME(EMPNO) AS PRIME
FROM EMP
WHERE EMPNO IN (7369, 7839, 7902)
ORDER BY EMPNO;

-- Test de la procédure P_Modif (attention: cela modifie réellement les données)
PROMPT === Test de la procédure P_Modif ===
-- Sauvegarde du salaire actuel de SMITH
SELECT 'Salaire actuel de SMITH: ' || SAL || '$' AS INFO
FROM EMP WHERE ENAME = 'SMITH';

-- Exécution de la procédure
BEGIN
    P_Modif('SMITH');
END;
/

-- Vérification après modification
SELECT 'Nouveau salaire de SMITH: ' || SAL || '$' AS INFO
FROM EMP WHERE ENAME = 'SMITH';

-- Test du trigger (ces instructions devraient échouer)
PROMPT === Test du trigger TR_SALAIRE_LIMITE ===
PROMPT Tentative d'insertion d'un employé avec salaire > 2500 (doit échouer):

-- Cette instruction doit échouer
BEGIN
    INSERT INTO EMP (EMPNO, ENAME, JOB, MGR, HIREDATE, SAL, DEPTNO)
    VALUES (9999, 'TEST', 'CLERK', 7902, SYSDATE, 3000, 20);
EXCEPTION
    WHEN OTHERS THEN
        DBMS_OUTPUT.PUT_LINE('Erreur attendue: ' || SQLERRM);
END;
/

PROMPT Tentative de mise à jour avec salaire > 2500 (doit échouer):
-- Cette instruction doit échouer
BEGIN
    UPDATE EMP SET SAL = 3000 WHERE ENAME = 'SMITH';
EXCEPTION
    WHEN OTHERS THEN
        DBMS_OUTPUT.PUT_LINE('Erreur attendue: ' || SQLERRM);
END;
/

-- =====================================================
-- SCRIPT DE REMISE À ZÉRO (optionnel)
-- =====================================================
/*
-- Pour remettre le salaire de SMITH à sa valeur originale
UPDATE EMP SET SAL = 800 WHERE ENAME = 'SMITH';
COMMIT;
*/
