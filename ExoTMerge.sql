--- <PERSON><PERSON> un Trigger plsql nommee t_merge qui synchronise les tables dept et departement en utilisant comme source dept après l'ajout de nouveaux départements dans dept
 create or replace TRIGGER t_merge
 AFTER INSERT ON dept 
BEGIN
    Merge into departement d
    using dept s
    on (s.deptno=d.deptno)
    when matched then Update
    set d.dname = s.dname, d.loc = s.loc
    when not matched then insert
    values (s.deptno,s.dname,s.loc);
END;
/
   

