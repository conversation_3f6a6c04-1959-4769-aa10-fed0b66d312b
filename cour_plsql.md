down

# PLSQL COURS

## [cite_start]Table des Matières 
- I) Connexion Oracle en Tant Qu'admin
- II) Voir les Bases de Données
- III) Ouvrir Container
- IV) Se Connecter en Tant que Scott
- V) Configuration SQL*PLUS
- VI) Script PLSQL

---

## I) Connexion Oracle en Tant Qu'admin

[cite_start]Pour se connecter en tant qu'administrateur système (`sysdba`), on peut utiliser la commande suivante en remplaçant "passer" par le mot de passe:

```sql
-- Entrez le nom utilisateur: 
Sys/passer as sysdba
Pour une connexion plus sécurisée, il est recommandé de ne pas inclure le mot de passe directement dans la commande. Oracle vous le demandera ensuite:

SQL

-- Entrez le nom utilisateur: 
Sys as sysdba
-- Entrez le mot de passe:
********
II) Voir les Bases de Données
Pour afficher les "Pluggable Databases" (PDBs) et leur statut, utilisez la commande show pdbs;.

SQL

SQL> show pdbs;
Exemple de sortie : 

CON_ID | CON_NAME | OPEN MODE   | RESTRICTED
-------|----------|-------------|-----------
2      | PDB$SEED | READ ONLY   | NO
3      | ORCLPDB  | MOUNTED     |
III) Ouvrir un Container (PDB)
Se placer sur le container (ici ORCLPDB):

SQL

SQL> ALTER SESSION SET CONTAINER=ORCLPDB;
Ouvrir la base de données "pluggable":

SQL

SQL> ALTER PLUGGABLE DATABASE OPEN;
Vérifier le statut : Après l'ouverture, on peut vérifier que le mode est READ WRITE en utilisant à nouveau SHOW PDBS;.

IV) Se Connecter en Tant que Scott
1. Vérifier si l'utilisateur existe
Pour lister les utilisateurs dans le container actuel:

SQL

SELECT USERNAME FROM dba_users;
2. Créer l'utilisateur (si nécessaire)
Note importante : Il faut que le container (PDB) soit ouvert en mode READ WRITE avant de créer un utilisateur, sinon vous obtiendrez une erreur ORA-65096.

Création de l'utilisateur : 

SQL

CREATE USER SCOTT IDENTIFIED BY TIGER;
Attribution des privilèges : 

SQL

GRANT CONNECT, RESOURCE, UNLIMITED TABLESPACE TO SCOTT;
3. Connexion
La chaîne de connexion doit spécifier l'utilisateur/mot de passe, l'hôte, le port et le nom du service de la PDB.

SQL

SQL> Conn SCOTT/TIGER@localhost:1521/ORCLPDB;
Erreur fréquente : ORA-12541: TNS: pas de processus d'écoute 
Cette erreur signifie que le "listener" Oracle n'est pas démarré ou n'écoute pas sur le port spécifié.


Solutions : 


Ouvrez un terminal système (en admin) et vérifiez le statut du listener:

Shell

lsnrctl status
La sortie de cette commande vous montrera le port correct (qui peut être 1522 au lieu de 1521).

Si le listener n'est pas actif, démarrez-le:

Shell

lsnrctl start
Vérifiez les fichiers de configuration listener.ora et tnsnames.ora dans le répertoire .../network/admin de votre installation Oracle.

V) Configuration SQL*PLUS
SET LINESIZE 120 : Définit le nombre de caractères par ligne à 120 pour éviter que le texte ne soit coupé.
SET PAGESIZE 60 : Définit le nombre de lignes à afficher par "page" de résultats.

SET SERVEROUTPUT ON : Indispensable pour afficher les messages provenant de DBMS_OUTPUT.PUT_LINE.
VI) Scripts PL/SQL
Bloc PL/SQL de base
Un bloc PL/SQL est structuré comme suit :

SQL

DECLARE
  -- Déclaration des variables (optionnel)
BEGIN
  -- Instructions exécutables
EXCEPTION
  -- Gestion des erreurs (optionnel)
END;
/
1. Variables et Entrées Utilisateur
Définir une variable en dur : 

SQL

DECLARE
  v_nom varchar2(25) := 'Salif';
  v_age number(2) := 23;
BEGIN
  Dbms_output.put_line('Bonjour '||v_nom||'; vous avez '||v_age||'ans');
END;
/
Demander une valeur à l'utilisateur (ACCEPT) : 

SQL

ACCEPT age PROMPT 'Quel est votre âge: ';

DECLARE
  v_age number(2) := &age; -- Utilisation de la variable de substitution
BEGIN
  Dbms_output.put_line('Vous avez ' || v_age || ' ans.');
END;
/
2. Interaction avec la base (SELECT, INSERT, DELETE)
SELECT INTO : Pour récupérer une seule ligne de résultat dans des variables. 

SQL

-- Affiche les infos de l'employé dont le matricule est fourni par l'utilisateur
ACCEPT mat PROMPT 'Quel est le matricule:';

DECLARE
  v_nom emp.ename%TYPE;       -- %TYPE : le type est hérité de la colonne
  v_fonction emp.job%TYPE;
  v_salaire emp.sal%TYPE;
BEGIN
  SELECT ename, job, sal
  INTO v_nom, v_fonction, v_salaire
  FROM emp
  WHERE empno = &mat;

  Dbms_output.put_line(v_nom || ' est un ' || v_fonction || ' avec un salaire de ' || v_salaire);
END;
/
INSERT : Pour ajouter des données. 

SQL

BEGIN
  INSERT INTO dept (deptno, dname, loc) VALUES (50, 'DEVELOPMENT', 'DAKAR');
END;
/
MERGE : Pour synchroniser deux tables (met à jour si la ligne existe, insère sinon). 

SQL

BEGIN
  MERGE INTO departement d         -- Table destination
  USING dept s                    -- Table source
  ON (s.deptno = d.deptno)      -- Condition de jointure
  WHEN MATCHED THEN
    UPDATE SET d.dname = s.dname, d.loc = s.loc
  WHEN NOT MATCHED THEN
    INSERT (deptno, dname, loc) VALUES (s.deptno, s.dname, s.loc);
END;
/
3. Structures de Contrôle
IF ELSIF ELSE : 

SQL

IF (v_salaire > 1500) THEN
  v_impot := v_salaire * 0.10;
ELSIF (v_salaire BETWEEN 1000 AND 1500) THEN
  v_impot := v_salaire * 0.05;
ELSE
  v_impot := v_salaire * 0.02;
END IF;
Boucle FOR : 

SQL

-- Calcule la somme des n premiers entiers
DECLARE
  v_somme NUMBER := 0;
BEGIN
  FOR i IN 1..&n LOOP
    v_somme := v_somme + i;
  END LOOP;
  Dbms_output.put_line('La somme est: ' || v_somme);
END;
/
Boucle WHILE : 

SQL

-- Affiche la table de multiplication par 7
DECLARE
  v_cpt NUMBER := 1;
BEGIN
  WHILE v_cpt <= 12 LOOP
    Dbms_output.put_line(v_cpt || ' * 7 = ' || v_cpt*7);
    v_cpt := v_cpt + 1;
  END LOOP;
END;
/
4. Curseurs
Un curseur est utilisé pour traiter ligne par ligne le résultat d'une requête qui renvoie plusieurs enregistrements.

SQL

-- Affiche le nom, la fonction et le salaire de tous les employés 
DECLARE
  -- 1. Déclaration du curseur
  CURSOR cur_emp IS SELECT ename, job, sal FROM emp;
  v_nom emp.ename%TYPE;
  v_fonction emp.job%TYPE;
  v_salaire emp.sal%TYPE;
BEGIN
  -- 2. Ouverture du curseur
  OPEN cur_emp;
  LOOP
    -- 3. Récupération de la ligne
    FETCH cur_emp INTO v_nom, v_fonction, v_salaire;
    -- 4. Condition de sortie
    EXIT WHEN cur_emp%NOTFOUND;
    -- Traitement de la ligne
    Dbms_output.put_line(v_nom || ' - ' || v_fonction || ' - $' || v_salaire);
  END LOOP;
  -- 5. Fermeture du curseur
  CLOSE cur_emp;
END;
/
5. Procédures
Une procédure est un bloc PL/SQL que l'on invoque avec EXECUTE. Elle n'a pas de valeur de retour.


SQL

CREATE OR REPLACE PROCEDURE p_employe (p_matricule IN emp.empno%TYPE)
IS
  v_nom emp.ename%TYPE;
  v_salaire emp.sal%TYPE;
BEGIN
  SELECT ename, sal INTO v_nom, v_salaire
  FROM emp WHERE empno = p_matricule;
  
  Dbms_output.put_line('Employé: ' || v_nom || ', Salaire: ' || v_salaire);
END;
/
Appel : 

SQL

EXEC p_employe(7902);
6. Fonctions
Une fonction est une procédure qui traite des instructions et retourne une valeur explicite.

SQL

CREATE OR REPLACE FUNCTION f_impot (p_salaire IN NUMBER)
RETURN NUMBER
IS
BEGIN
  -- Retourne une valeur (ici, 5% du salaire)
  RETURN p_salaire * 0.05;
END;
/
Appel dans un SELECT : 

SQL

SELECT ename, sal, f_impot(sal) AS impot
FROM emp;
7. Triggers (Déclencheurs)
Un trigger est une procédure qui s'exécute automatiquement lorsqu'un évènement se produit.

Trigger de niveau ligne (FOR EACH ROW) : S'exécute pour chaque ligne affectée. Permet d'accéder aux valeurs avant (:OLD) et après (:NEW) la modification.

SQL

-- Empêche que le salaire d'un nouvel employé soit en dehors d'un intervalle 
CREATE OR REPLACE TRIGGER t_verifsal
BEFORE INSERT ON emp
FOR EACH ROW
BEGIN
  IF :NEW.sal NOT BETWEEN 750 AND 10000 THEN
    RAISE_APPLICATION_ERROR(-20011, 'Le salaire doit être compris entre 750$ et 10000$');
  END IF;
END;
/
Trigger de niveau table : S'exécute une seule fois pour l'instruction, quelle que soit le nombre de lignes affectées.

SQL

-- Interdit les transactions en dehors des heures ouvrables 
CREATE OR REPLACE TRIGGER t_securite
BEFORE INSERT OR UPDATE OR DELETE ON emp
BEGIN
  IF TO_CHAR(sysdate, 'DY', 'NLS_DATE_LANGUAGE=ENGLISH') IN ('SAT', 'SUN')
    OR TO_CHAR(sysdate, 'HH24') NOT BETWEEN '08' AND '16'
  THEN
    RAISE_APPLICATION_ERROR(-20005, 'Transaction interdite en dehors des heures de bureau.');
  END IF;
END;
/
Trigger de niveau base de données : Déclenché par des événements système comme LOGON ou LOGOFF.

SQL

-- Audite les connexions des utilisateurs 
CREATE OR REPLACE TRIGGER t_connexion
AFTER LOGON ON SCHEMA -- ou ON DATABASE
BEGIN
  INSERT INTO t_audit (message)
  VALUES (USER || ' s''est connecté le ' || SYSDATE);
END;
/