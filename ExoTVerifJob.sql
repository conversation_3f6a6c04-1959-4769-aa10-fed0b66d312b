--Creer un Trigger plsql nommee t_verifjob qui interdit que la fonction d'un employé ajouté ou modifié ou supprimé est différente de Président Ou Manager
CREATE or REPLACE TRIGGER t_verifjob
AFTER INSERT OR UPDATE OR DELETE ON emp 
FOR EACH ROW
BEGIN
    IF (:NEW.job) IN ('PRESIDENT','MANAGER') 
    OR (:OLD.job) IN ('PRESIDENT','MANAGER') 
    THEN
        RAISE_APPLICATION_ERROR(-20013,'Operation interdite sur la fonction de President ou Manager');
    END IF;         
END;
/
   

