--- <PERSON><PERSON> un Trigger plsql nommee t_securite qui interdit toutes opérations LMD sur la table emp aux heures et jours non ouvrables
 create or replace TRIGGER t_securite
 AFTER INSERT OR UPDATE OR DELETE ON emp 
BEGIN
    IF To_char(sysdate,'fmday') in ('samedi','dimanche')
        OR To_char(sysdate,'hh24:mi') not BETWEEN '08:00' AND '16:00' then 
        RAISE_APPLICATION_ERROR(-20005,'Transaction Interdite');
    END IF;
END;
/
   

