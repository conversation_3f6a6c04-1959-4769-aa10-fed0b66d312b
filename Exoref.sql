---<PERSON><PERSON>er un script PLSQL qui affiche le nom,la fonction et le salaire de l'employé dont le matricule est defini par l'utilisateur
ACCEPT mat prompt 'Quelle est le matricule:';
DECLARE
    v_nom emp.ename%TYPE;
    v_fonction emp.job%TYPE;
    v_salaire emp.sal%TYPE;
BEGIN
    SELECT ename,job,sal
    INTO v_nom,v_fonction,v_salaire
    FROM emp
    WHERE empno=&mat;
    Dbms_output.Put_line(v_nom||' est un '||v_fonction||' avec un salaire de $'||v_salaire);
END;
/