--- <PERSON><PERSON> un Trigger plsql nommee t_predicat qui interdit toutes opérations LMD sur la table emp aux heures et jours non ouvrables
CREATE or REPLACE TRIGGER t_predicat
AFTER INSERT OR UPDATE OR DELETE ON emp 
BEGIN
    IF To_char(sysdate,'fmday') in ('samedi','dimanche')
        OR To_char(sysdate,'hh24:mi') not BETWEEN '08:00' AND '16:00' then 
            IF INSERTING THEN
                RAISE_APPLICATION_ERROR(-20006,'INSERTION Interdite');
            ELSIF UPDATING THEN
                RAISE_APPLICATION_ERROR(-20007,'MODIFICATION Interdite');
            ELSIF DELETING THEN
                RAISE_APPLICATION_ERROR(-20008,'SUPPRESSION Interdite');
            END IF;
    END IF;
END;
/
   

