
Markdown

# EXAMEN ORACLE - PLSQL

**Prof:** Bobo DIALLO
**Durée:** 3H00

---

## Schéma de la base de données

[cite_start]Considérons le schéma de SCOTT avec les tables **EMP** et **DEPT**.

### Table DEPT

[cite_start]`DEPT (DEPTNO, DNAME, LOC)` 

* [cite_start]**DEPTNO**: le numéro du département 
* [cite_start]**DNAME**: le nom du département 
* [cite_start]**LOC**: la localité du département 

**Données DEPT :**
DEPTNO	DNAME	LOC
10	ACCOUNTING	NEW YORK
20	RESEARCH	DALLAS
30	SALES	CHICAGO
40	OPERATIONS	BOSTON

Exporter vers Sheets


### Table EMP

`EMP (EMPNO, ENAME, JOB, MGR, HIREDATE, SAL, COMM, DEPTNO)` 

* **EMPNO**: le matricule de l'employé 
* **ENAME**: son nom 
* **JOB**: sa fonction 
* **MGR**: le matricule de son supérieur 
* **HIREDATE**: sa date d'embauche 
* **SAL**: son salaire 
* **COMM**: sa commission s'il est vendeur (SALESMAN) 
* **DEPTNO**: la colonne clé étrangère 

**Données EMP :**
EMPNO	ENAME	JOB	MGR	HIREDATE	SAL	COMM	DEPTNO
7369	SMITH	CLERK	7902	17/12/80	800		20
7499	ALLEN	SALESMAN	7698	20/02/81	1600	300	30
7521	WARD	SALESMAN	7698	22/02/81	1250	500	30
7566	JONES	MANAGER	7839	02/04/81	2975		20
7654	MARTIN	SALESMAN	7698	28/09/81	1250	1400	30
7698	BLAKE	MANAGER	7839	01/05/81	2850		30
7782	CLARK	MANAGER	7839	09/06/81	2450		10
7839	KING	PRESIDENT		17/11/81	5000		10
7844	TURNER	SALESMAN	7698	08/09/81	1500	0	30
7900	JAMES	CLERK	7698	03/12/81	950		30
7902	FORD	ANALYST	7566	03/12/81	3000		20
7934	MILLER	CLERK	7782	23/01/82	1300		10

Exporter vers Sheets

*NB: Toutes les données alphanumériques sont en majuscule*.

---

## Questions

1.  Écrire un programme qui permet de convertir un montant en FCFA en une devise étrangère. Le montant à convertir et le taux de change seront définis par l'utilisateur.

2.  Créer une fonction `f_employes` qui reçoit le matricule et affiche le nom, la fonction et le Salaire de l'employé.

3.  Écrire la fonction nommée `F_PRIME` qui permet de calculer la prime dont le taux est de 20% du salaire de tout employé dont le matricule sera spécifié.

4.  Écrire l'instruction SQL `SELECT` qui permet d'appeler la fonction de l'Exo N°3 pour afficher le matricule, le nom, le salaire mensuel, la prime et le salaire net de chaque employé de la table EMP de SCOTT.

5.  Créer une procédure `P_Modif` qui permet d'augmenter de 500$ le salaire d'un employé de la table EMP de SCOTT dont le nom sera défini par l'utilisateur au moment de l'exécution de la procédure.

6.  Créer un trigger qui annule toute opération LMD sur un employé dont le salaire est supérieur à 2500.
