-- Solutions du Devoir PL/SQL L3GL G2 ISI
-- Prof: Bobo DIALLO
-- Schéma SCOTT avec tables EMP et DEPT

-- =====================================================
-- 1°) Programme de conversion de montant FCFA vers devise étrangère
-- =====================================================

DECLARE
    v_montant_fcfa NUMBER;
    v_taux_change NUMBER;
    v_montant_converti NUMBER;
    v_devise VARCHAR2(10);
BEGIN
    -- Saisie des données par l'utilisateur
    v_montant_fcfa := &montant_fcfa;
    v_taux_change := &taux_change;
    v_devise := '&devise';
    
    -- Calcul de la conversion
    v_montant_converti := v_montant_fcfa / v_taux_change;
    
    -- Affichage du résultat
    DBMS_OUTPUT.PUT_LINE('Montant en FCFA: ' || v_montant_fcfa);
    DBMS_OUTPUT.PUT_LINE('Taux de change: ' || v_taux_change);
    DBMS_OUTPUT.PUT_LINE('Montant converti en ' || v_devise || ': ' || ROUND(v_montant_converti, 2));
END;
/

-- =====================================================
-- 2°) Fonction f_employes qui affiche les infos d'un employé
-- =====================================================

CREATE OR REPLACE FUNCTION f_employes(p_empno IN NUMBER)
RETURN VARCHAR2
IS
    v_ename EMP.ENAME%TYPE;
    v_job EMP.JOB%TYPE;
    v_sal EMP.SAL%TYPE;
    v_result VARCHAR2(200);
BEGIN
    -- Récupération des données de l'employé
    SELECT ENAME, JOB, SAL
    INTO v_ename, v_job, v_sal
    FROM EMP
    WHERE EMPNO = p_empno;
    
    -- Construction du résultat
    v_result := 'Nom: ' || v_ename || ', Fonction: ' || v_job || ', Salaire: ' || v_sal;
    
    -- Affichage
    DBMS_OUTPUT.PUT_LINE(v_result);
    
    RETURN v_result;
    
EXCEPTION
    WHEN NO_DATA_FOUND THEN
        DBMS_OUTPUT.PUT_LINE('Aucun employé trouvé avec le matricule: ' || p_empno);
        RETURN 'Employé non trouvé';
    WHEN OTHERS THEN
        DBMS_OUTPUT.PUT_LINE('Erreur: ' || SQLERRM);
        RETURN 'Erreur lors de la recherche';
END f_employes;
/

-- =====================================================
-- 3°) Fonction F_PRIME pour calculer la prime (20% du salaire)
-- =====================================================

CREATE OR REPLACE FUNCTION F_PRIME(p_empno IN NUMBER)
RETURN NUMBER
IS
    v_sal EMP.SAL%TYPE;
    v_prime NUMBER;
BEGIN
    -- Récupération du salaire de l'employé
    SELECT SAL
    INTO v_sal
    FROM EMP
    WHERE EMPNO = p_empno;
    
    -- Calcul de la prime (20% du salaire)
    v_prime := v_sal * 0.20;
    
    RETURN v_prime;
    
EXCEPTION
    WHEN NO_DATA_FOUND THEN
        RETURN 0;
    WHEN OTHERS THEN
        RETURN 0;
END F_PRIME;
/

-- =====================================================
-- 4°) Instruction SELECT pour afficher matricule, nom, salaire, prime et salaire net
-- =====================================================

SELECT
    EMPNO as "Matricule",
    ENAME as "Nom",
    SAL as "Salaire Mensuel",
    F_PRIME(EMPNO) as "Prime (20%)",
    (SAL + F_PRIME(EMPNO)) as "Salaire Net"
FROM EMP
ORDER BY EMPNO;

-- =====================================================
-- 5°) Procédure P_Modif pour augmenter le salaire de 500$
-- =====================================================

CREATE OR REPLACE PROCEDURE P_Modif(p_ename IN VARCHAR2)
IS
    v_count NUMBER;
BEGIN
    -- Vérifier si l'employé existe
    SELECT COUNT(*)
    INTO v_count
    FROM EMP
    WHERE UPPER(ENAME) = UPPER(p_ename);

    IF v_count = 0 THEN
        DBMS_OUTPUT.PUT_LINE('Aucun employé trouvé avec le nom: ' || p_ename);
    ELSE
        -- Augmenter le salaire de 500$
        UPDATE EMP
        SET SAL = SAL + 500
        WHERE UPPER(ENAME) = UPPER(p_ename);

        DBMS_OUTPUT.PUT_LINE('Salaire de ' || p_ename || ' augmenté de 500$');
        DBMS_OUTPUT.PUT_LINE(SQL%ROWCOUNT || ' ligne(s) mise(s) à jour');

        COMMIT;
    END IF;

EXCEPTION
    WHEN OTHERS THEN
        DBMS_OUTPUT.PUT_LINE('Erreur lors de la modification: ' || SQLERRM);
        ROLLBACK;
END P_Modif;
/

-- =====================================================
-- 6°) Trigger pour annuler les opérations LMD sur employés avec salaire > 2500
-- =====================================================

CREATE OR REPLACE TRIGGER TR_SALAIRE_LIMITE
    BEFORE INSERT OR UPDATE OR DELETE ON EMP
    FOR EACH ROW
BEGIN
    -- Pour INSERT et UPDATE, vérifier le nouveau salaire
    IF INSERTING OR UPDATING THEN
        IF :NEW.SAL > 2500 THEN
            RAISE_APPLICATION_ERROR(-20001,
                'Opération interdite: Le salaire ne peut pas dépasser 2500$');
        END IF;
    END IF;

    -- Pour DELETE, vérifier l'ancien salaire
    IF DELETING THEN
        IF :OLD.SAL > 2500 THEN
            RAISE_APPLICATION_ERROR(-20002,
                'Opération interdite: Impossible de supprimer un employé avec un salaire > 2500$');
        END IF;
    END IF;
END TR_SALAIRE_LIMITE;
/

-- =====================================================
-- EXEMPLES D'UTILISATION
-- =====================================================

-- Test de la fonction f_employes
BEGIN
    DBMS_OUTPUT.PUT_LINE('Test fonction f_employes:');
    DBMS_OUTPUT.PUT_LINE(f_employes(7369)); -- SMITH
END;
/

-- Test de la procédure P_Modif
BEGIN
    DBMS_OUTPUT.PUT_LINE('Test procédure P_Modif:');
    P_Modif('SMITH');
END;
/

-- Vérification après modification
SELECT EMPNO, ENAME, SAL FROM EMP WHERE ENAME = 'SMITH';

-- Test du trigger (cette instruction devrait échouer si le salaire dépasse 2500)
-- INSERT INTO EMP VALUES (9999, 'TEST', 'CLERK', 7902, SYSDATE, 3000, NULL, 20);
